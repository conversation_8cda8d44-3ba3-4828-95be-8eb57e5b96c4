/* 
 * ملف المتغيرات الموحدة للمشروع
 * CSS Variables File for Unified Project Styling
 */

:root {
  /* ========================================= */
  /* الخطوط - Fonts */
  /* ========================================= */
  
  /* الخط الأساسي للمشروع */
  --font-family-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  
  /* أحجام الخطوط - Font Sizes */
  --font-size-xs: 8px;       /* للنصوص الصغيرة جداً */
  --font-size-sm: 9px;       /* للنصوص الصغيرة */
  --font-size-base: 11px;    /* الحجم الأساسي */
  --font-size-md: 16px;      /* للنصوص المتوسطة */
  --font-size-lg: 18px;      /* للنصوص الكبيرة */
  --font-size-xl: 20px;      /* للعناوين الصغيرة */
  --font-size-2xl: 22px;     /* للعناوين المتوسطة */
  --font-size-3xl: 24px;     /* للعناوين الكبيرة */
  --font-size-4xl: 28px;     /* للعناوين الكبيرة جداً */
  
  /* أوزان الخطوط - Font Weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* ارتفاع الأسطر - Line Heights */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* ========================================= */
  /* الألوان - Colors */
  /* ========================================= */
  
  /* الألوان الأساسية للمشروع */
  --primary-color: #2563eb;
  --primary-dark-blue: #2a2e70;
  --primary-medium-blue: #4c68c0;
  --secondary-color: #10b981;
  --secondary-dark: #059669;
  
  /* الألوان المحايدة */
  --neutral-50: #f8fafc;
  --neutral-100: #f1f5f9;
  --neutral-200: #e2e8f0;
  --neutral-300: #cbd5e1;
  --neutral-400: #94a3b8;
  --neutral-500: #64748b;
  --neutral-600: #475569;
  --neutral-700: #334155;
  --neutral-800: #1e293b;
  --neutral-900: #0f172a;
  
  /* ألوان الحالة */
  --success-color: #10b981;
  --success-dark: #059669;
  --success-50: #f0fdf4;
  --success-200: #bbf7d0;
  --success-700: #15803d;
  --warning-50: #fffbeb;
  --warning-200: #fed7aa;
  --warning-700: #c2410c;
  --error-50: #fef2f2;
  --error-200: #fecaca;
  --error-700: #b91c1c;
  --info: #3b82f6;
  --info-50: #eff6ff;
  --info-200: #bfdbfe;
  --info-700: #1d4ed8;
  
  /* ألوان إضافية */
  --light-purple-gray: #dad1de;
  --dark-blue-gray: #555269;
  --white: #ffffff;
  
  /* ألوان البطاقات المتخصصة - Specialized Card Colors */
  --parties-color: #1a365d;
  --parties-dark: #2d3748;
  --identification-color: #744210;
  --identification-dark: #553c0f;
  --location-color: #1a202c;
  --location-dark: #2d3748;
  --timeline-color: #f7fafc;
  --timeline-dark: #e2e8f0;
  
  /* ========================================= */
  /* المسافات - Spacing */
  /* ========================================= */
  
  --space-1: 4px;
  --space-2: 8px;
  --space-3: 12px;
  --space-4: 16px;
  --space-5: 20px;
  --space-6: 24px;
  --space-8: 32px;
  --space-10: 40px;
  --space-12: 48px;
  
  /* ========================================= */
  /* الزوايا - Border Radius */
  /* ========================================= */
  
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  
  /* ========================================= */
  /* الظلال - Shadows */
  /* ========================================= */
  
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-light: rgba(42, 46, 112, 0.1);
  --shadow-medium: rgba(42, 46, 112, 0.2);
  --shadow-heavy: rgba(42, 46, 112, 0.3);
  
  /* ========================================= */
  /* الانتقالات - Transitions */
  /* ========================================= */
  
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* ========================================= */
/* الحركات المشتركة - Common Animations */
/* ========================================= */

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(2deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.9; }
  50% { transform: scale(1.05); opacity: 1; }
}

@keyframes modernSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes shimmer {
  0% {
    transform: rotate(45deg) translate(-100%, -100%);
  }
  50% {
    transform: rotate(45deg) translate(0%, 0%);
  }
  100% {
    transform: rotate(45deg) translate(100%, 100%);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from { 
    opacity: 0; 
  }
  to { 
    opacity: 1; 
  }
}

/* ========================================= */
/* التدرجات المشتركة - Common Gradients */
/* ========================================= */

:root {
  /* تدرجات الألوان الأساسية */
  --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  --gradient-primary-reverse: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
  --gradient-secondary: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
  
  /* تدرجات الخلفيات */
  --gradient-bg-light: linear-gradient(135deg, var(--neutral-50) 0%, var(--neutral-200) 100%);
  --gradient-bg-white: linear-gradient(135deg, #ffffff, #f8fafc);
  
  /* تدرجات الأزرار */
  --gradient-button-primary: linear-gradient(135deg, var(--primary-medium-blue) 0%, var(--primary-dark-blue) 100%);
  --gradient-button-success: linear-gradient(135deg, #28a745, #20c997);
  --gradient-button-danger: linear-gradient(135deg, #dc3545, #c82333);
  
  /* تدرجات الحالات */
  --gradient-parties: linear-gradient(135deg, var(--parties-color), var(--parties-dark));
  --gradient-identification: linear-gradient(135deg, var(--identification-color), var(--identification-dark));
  --gradient-location: linear-gradient(135deg, var(--location-color), var(--location-dark));
  
  /* المسافات المشتركة - Common Paddings */
  --padding-button: 12px 20px;
  --padding-card: 20px;
  --padding-section: 16px 20px;
  --padding-form: 15px 20px;
  
  /* الحدود المشتركة - Common Border Radius */
  --border-radius-sm: 8px;
  --border-radius-md: 12px;
  --border-radius-lg: 15px;
  --border-radius-xl: 20px;
  
  /* الظلال المشتركة - Common Box Shadows */
  --box-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --box-shadow-md: 0 2px 8px rgba(0, 0, 0, 0.1);
  --box-shadow-lg: 0 4px 12px rgba(0, 0, 0, 0.15);
  --box-shadow-xl: 0 8px 25px rgba(0, 0, 0, 0.2);
  --box-shadow-primary: 0 2px 8px rgba(76, 104, 192, 0.2);
  --box-shadow-danger: 0 2px 8px rgba(220, 38, 38, 0.1);
  
  /* الانتقالات المشتركة - Common Transitions */
  --transition-all: all 0.3s ease;
  --transition-transform: transform 0.3s ease;
  --transition-opacity: opacity 0.3s ease;
  --transition-colors: background-color 0.3s ease, color 0.3s ease;
  
  /* التحويلات المشتركة - Common Transforms */
  --transform-hover-up: translateY(-2px);
  --transform-hover-scale: scale(1.05);
  
  /* المسافات الإضافية - Additional Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 24px;
  --spacing-2xl: 32px;
  
  /* الزوايا الإضافية - Additional Border Radius */
  --radius-full: 50%;
  
  /* الألوان الأساسية المحدثة - Updated Primary Colors */
  --primary-dark: #1d4ed8;
  --primary-light: #dbeafe;
  
  /* الأنيميشنات المشتركة - Common Animations */
  --animation-shimmer: shimmer 3s infinite;
  --animation-slide-down: slideDown 0.2s ease-out;
  --animation-slide-in: slideIn 0.5s ease;
  --animation-fade-in: fadeIn 0.3s ease-in-out;
  --animation-spin: modernSpin 1s linear infinite;
  
  /* نقاط التوقف للتصميم المتجاوب - Responsive Breakpoints */
  --breakpoint-mobile: 480px;
  --breakpoint-tablet: 768px;
  --breakpoint-desktop: 1024px;
  --breakpoint-large: 1200px;
  
  /* لون الخلفية الموحد لجميع الصفحات - Unified Page Background */
  --page-background: var(--neutral-100);
  --page-background-alt: var(--neutral-50);
  
  /* تصميم الكروت الموحد - Unified Card Design */
  --card-bg: white;
  --card-border: 1px solid var(--neutral-200);
  --card-border-radius: var(--border-radius-md);
  --card-shadow: var(--box-shadow-md);
  --card-shadow-hover: var(--box-shadow-lg);
  --card-padding: 1.5rem;
  
  /* تصميم رؤوس الكروت الموحد - Unified Card Headers */
  --card-header-bg: linear-gradient(135deg, #014871 0%, #4a8fa3 50%, #d7ede2 100%);
  --card-header-padding: var(--padding-section);
  --card-header-border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
  --card-header-text-color: white;
  --card-header-text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  
  /* تصميم محتوى الكروت الموحد - Unified Card Content */
  --card-content-bg: linear-gradient(135deg, rgba(1, 72, 113, 0.08) 0%, rgba(74, 143, 163, 0.12) 50%, rgba(215, 237, 226, 0.15) 100%);
  --card-content-border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
}

/* تطبيق الخط الأساسي على جميع العناصر */
* {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}