
@import '../styles/variables.css';

/* تخصيص خاص بصفحة لوحة التحكم */
.dashboard {
  font-family: var(--font-family-primary);
  background-color: var(--page-background);
  color: var(--neutral-900);
  line-height: var(--line-height-normal);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.dashboard h1, 
.dashboard h2, 
.dashboard h3, 
.dashboard h4, 
.dashboard h5, 
.dashboard h6 {
  font-weight: var(--font-weight-medium);
  color: var(--neutral-900);
}

.content {
  flex: 1;
  padding: 2rem; /* Consistent padding */
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  text-align: center;
}

/* Header Section */
.header {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 30px;
  margin-bottom: 2rem;
}

/* Logo Container */
.logoContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
}

.mainLogo {
  height: 120px;
  width: auto;
  max-width: 100%;
  object-fit: contain;
}

/* Search Container */
.searchContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-width: 600px;
}

.searchBox {
  position: relative;
  width: 100%;
  max-width: 500px;
}

.searchIcon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #94a3b8;
  font-size: 18px;
  z-index: 1;
}

.searchInput {
  width: 100%;
  padding: 15px 50px 15px 20px;
  border: 2px solid #e2e8f0;
  border-radius: 25px;
  font-size: 16px;
  background-color: #ffffff;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.searchInput:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.searchInput::placeholder {
  color: #94a3b8;
  font-size: 16px;
}

h1 {
  font-size: 2.25rem; /* Larger, more prominent heading */
  font-weight: 400; /* Lighter weight for main title */
  color: var(--google-grey-700);
}

/* Account Status Card */
.accountStatus {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 14px 24px; /* More padding */
  background-color: var(--google-white);
  border-radius: var(--radius-md);
  font-size: 1rem;
  color: var(--google-grey-700);
  flex-wrap: wrap;
  justify-content: center;
  box-shadow: var(--shadow-1); /* Subtle shadow */
  border: 1px solid var(--google-grey-200);
}

.onlineText {
  color: var(--google-blue-500); /* Google Blue for online */
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500; /* Medium weight */
}

.localText {
  color: var(--google-green-500); /* Google Green for local */
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
}

.accountIcon {
  font-size: 1.1em;
}

.accountNote {
  font-size: 0.85rem;
  color: var(--google-grey-500);
  margin-left: 10px;
}

/* Stats Section */
.stats {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
}

.statCard {
  background-color: var(--google-white);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-1);
  border: 1px solid var(--google-grey-200);
  text-align: center;
  min-width: 200px; /* Ensure a decent size */
  transition: all var(--transition-normal);
}

.statCard:hover {
  box-shadow: var(--shadow-2); /* Slightly elevate on hover */
}

.statValue {
  font-size: 3rem; /* Large, prominent number */
  font-weight: 300; /* Light weight for the number */
  color: var(--google-blue-600); /* Highlight with Google Blue */
  display: block;
  margin-bottom: 0.5rem;
}

.statLabel {
  font-size: 1.1rem;
  color: var(--google-grey-700);
  font-weight: 500;
}

/* Grid for Navigation Cards */
.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

/* Spacing for Assigned Tasks Section */
.assignedTasksSection {
  margin-top: 3rem; /* إضافة مسافة أكبر بين كارت القضايا والمهام المكلفة */
}

/* Navigation Cards */
.card {
  background: linear-gradient(145deg, var(--page-background), #dad1de08);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 
    0 20px 60px var(--shadow-medium),
    0 10px 30px var(--shadow-light);
  cursor: pointer;
  transition: all var(--transition-normal);
  text-align: center;
  border: 1px solid #dad1de;
  position: relative;
  overflow: hidden;
  animation: var(--animation-fade-in);
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 
    0 25px 70px rgba(42, 46, 112, 0.15),
    0 15px 40px rgba(76, 104, 192, 0.1);
  border-color: #4c68c0;
  background: linear-gradient(145deg, var(--page-background), #dad1de15);
}

/* إزالة الشريط العلوي */
.card::before {
  content: none;
}

.card h3 {
  font-size: 1.5rem;
  font-weight: var(--font-weight-medium);
  margin-bottom: 0.75rem;
  background: linear-gradient(135deg, #2a2e70, #4c68c0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: #2a2e70; /* fallback للمتصفحات التي لا تدعم background-clip */
}

.card p {
  font-size: 1rem;
  color: #555269;
}

/* Loading Spinner */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loadingSpinner {
  border: 4px solid var(--google-grey-200);
  border-left: 4px solid var(--google-blue-500); /* Google Blue for spinner */
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

/* Arabic Specific Styles (ensure consistency with Material Design) */
.arabicContainer {
  direction: rtl;
  font-family: var(--font-family-primary);
  padding: 24px;
  background-color: var(--google-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-1);
  flex: 1;
  color: var(--google-grey-900);
  border: 1px solid var(--google-grey-200);
}

.arabicHeader {
  display: flex;
  align-items: center;
  margin-bottom: 28px;
}

.arabicBackButton {
  display: flex;
  align-items: center;
  background-color: var(--google-grey-100);
  padding: 10px 16px;
  border-radius: var(--radius-md);
  cursor: pointer;
  color: var(--google-grey-700);
  transition: all var(--transition-normal);
  border: 1px solid var(--google-grey-200);
  font-weight: 500;
}

.arabicBackButton:hover {
  background-color: var(--google-grey-200);
  transform: translateY(-2px);
  color: var(--google-grey-900);
}

.arabicButtonIcon {
  margin-left: 8px;
}

.arabicTitle {
  margin: 0;
  font-size: 24px;
  color: var(--google-grey-900);
  flex-grow: 1;
  font-weight: 600;
}

.arabicReportsSection {
  margin-bottom: 25px;
}

.arabicReportCard {
  background-color: var(--google-white);
  border: 1px solid var(--google-grey-200);
  border-radius: var(--radius-md);
  padding: 16px;
  margin-bottom: 16px;
  border-right: 4px solid var(--google-blue-500); /* Use Google Blue for accent */
  box-shadow: var(--shadow-1);
  transition: all var(--transition-normal);
}

.arabicReportCard:hover {
  box-shadow: var(--shadow-2);
  border-color: var(--google-blue-600); /* Darker blue on hover */
}

.arabicReportDate {
  font-weight: 600;
  color: var(--google-grey-900);
  margin-bottom: 8px;
  font-size: 16px;
}

.arabicReportContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.arabicReportText {
  color: var(--google-grey-700);
  line-height: 1.6;
  font-size: 15px;
}

.arabicHistoryButton {
  background: none;
  border: none;
  color: var(--google-blue-500); /* Google Blue for buttons */
  cursor: pointer;
  font-size: 16px;
  margin-right: 10px;
  transition: all var(--transition-normal);
  width: auto;
  padding: 6px 12px;
  border-radius: var(--radius-sm); /* Slightly rounded button */
}

.arabicHistoryButton:hover {
  color: var(--google-blue-600);
  background-color: rgba(66, 133, 244, 0.1); /* Light blue hover background */
  border-radius: var(--radius-md);
}

.arabicDivider {
  margin: 28px 0;
  display: flex;
  justify-content: center;
}

.arabicLine {
  width: 100%;
  height: 1px;
  background-color: var(--google-grey-200);
}

.arabicActionsSection {
  margin-top: 20px;
}

.arabicSectionTitle {
  font-size: 20px;
  color: var(--google-grey-900);
  margin-bottom: 15px;
}

.arabicActionCard {
  background-color: var(--google-white);
  border: 1px solid var(--google-grey-200);
  border-radius: var(--radius-md);
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: var(--shadow-1);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.arabicActionCard:hover {
  box-shadow: var(--shadow-2);
  border-color: var(--google-blue-500);
}

.arabicActionCard::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
  background-color: var(--google-blue-500); /* Google Blue accent */
}

.arabicActionTitle {
  font-weight: 600;
  color: var(--google-grey-900);
  margin-bottom: 10px;
  font-size: 16px;
}

.arabicActionDetails {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.arabicActionDeadline {
  color: var(--google-grey-700);
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
}

/* Responsive adjustments */
@media (min-width: 1200px) {
  .content {
    padding: 3rem; /* Even more padding on large screens */
  }
  .grid {
    gap: 2rem;
  }

}

@media (max-width: 991px) {
  .content {
    padding: 1.5rem;
  }
  .grid {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 1.25rem;
  }
  
  h1 {
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  .content {
    padding: 1rem;
  }
  .grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  h1 {
    font-size: 1.75rem;
  }
  .statValue {
    font-size: 2.5rem;
  }
  .statLabel {
    font-size: 1rem;
  }
  .accountStatus {
    font-size: 0.9rem;
    padding: 10px 16px;
  }
  .assignedTasksSection {
    margin-top: 2rem; /* مسافة أقل في الشاشات الصغيرة */
  }
  
  /* تحسين الشعار وخانة البحث للهواتف */
  .mainLogo {
    height: 80px;
  }
  
  .searchInput {
    padding: 12px 45px 12px 15px;
    font-size: 14px;
  }
  
  .searchIcon {
    right: 12px;
    font-size: 16px;
  }
  
  .header {
    gap: 20px;
  }
}

@media (max-width: 576px) {
  .content {
    padding: 0.75rem;
  }
  .grid {
    margin-top: 1.5rem;
  }

  h1 {
    font-size: 1.5rem;
  }
  p {
    font-size: 0.9rem;
  }
  .statValue {
    font-size: 2rem;
  }
  .statLabel {
    font-size: 0.9rem;
  }
  .accountNote {
    margin-left: 0;
    margin-top: 5px; /* Move to new line if wrapped */
    text-align: center;
    width: 100%;
  }
  .assignedTasksSection {
    margin-top: 1.5rem; /* مسافة أقل في الشاشات الصغيرة جداً */
  }
  
  /* تحسين إضافي للهواتف الصغيرة */
  .mainLogo {
    height: 60px;
  }
  
  .searchInput {
    padding: 10px 40px 10px 12px;
    font-size: 14px;
  }
  
  .searchIcon {
    right: 10px;
    font-size: 14px;
  }
  
  .header {
    gap: 15px;
  }
}